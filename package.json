{"name": "tampermonkey-userscripts", "description": "A collection of Tampermonkey userscripts for enhancing the reading experience on various websites.", "version": "1.0.0", "private": true, "license": "GPL-3.0", "author": "iPumpkin <https://github.com/ipumpkin17>", "type": "module", "packageManager": "pnpm@10.13.0", "scripts": {"dev": "vite", "build": "vite build", "postbuild": "node tools/compress.js -i dist/tampermonkey-userscripts.user.js -o dist/edewakaru.user.js", "preview": "vite preview", "compress": "node tools/compress.js -i src/edewakaru.js", "update-version": "node tools/update-version.js && git add vite.config.js && git commit -m 'chore: update version'", "publish": "git checkout main && git rebase origin/dev && git push"}, "devDependencies": {"@babel/generator": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/traverse": "^7.28.0", "chalk": "^5.4.1", "prettier": "^3.6.2", "vite": "^7.0.4", "vite-plugin-monkey": "^5.0.9", "yargs": "^18.0.0"}}