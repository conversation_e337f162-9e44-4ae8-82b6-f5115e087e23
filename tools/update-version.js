#!/usr/bin/env node
// 脚本：update-version.js
// 功能：自动更新版本号为当前日期格式（YYYY.MM.DD）
// 用法：node update-version.js

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// 获取当前模块路径
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 获取项目根目录
const rootDir = path.join(__dirname, '..')

// 获取当前日期，格式为 YYYY.MM.DD
function getCurrentDateVersion() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}.${month}.${day}`
}

// 更新 vite.config.js 中的版本号
function updateVersion() {
  const configPath = path.join(rootDir, 'vite.config.js')

  // 读取配置文件
  let configContent = fs.readFileSync(configPath, 'utf8')

  // 获取当前日期版本
  const newVersion = getCurrentDateVersion()

  // 使用正则表达式替换版本号
  const versionRegex = /(version:\s*['"])([^'"]+)(['"])/
  const updatedContent = configContent.replace(versionRegex, `$1${newVersion}$3`)

  // 写回文件
  fs.writeFileSync(configPath, updatedContent)

  console.log(`版本号已更新为: ${newVersion}`)
}

// 执行更新
updateVersion()
