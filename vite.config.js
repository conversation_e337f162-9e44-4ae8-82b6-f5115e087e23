import { defineConfig } from 'vite'
import monkey from 'vite-plugin-monkey'

export default defineConfig({
  esbuild: {
    minify: false,
    drop: ['debugger'],
    pure: ['console.log', 'console.info', 'console.debug', 'console.warn', 'console.error'],
  },
  plugins: [
    monkey({
      entry: 'src/main.js',
      userscript: {
        // name: 'Edewaka<PERSON> Enhanced',
        name: {
          en: 'Edewakaru Enhanced',
          ja: '「絵でわかる日本語」 閲覧体験強化',
          'zh-CN': '「絵でわかる日本語」 阅读体验增强',
          'zh-TW': '「絵でわかる日本語」 閱讀體驗增強',
          ko: '「絵でわかる日本語」 독서 경험 향상',
          vi: '「絵でわかる日本語」 Nâng Cao Trải Nghiệm Đọc',
        },
        namespace: 'https://greasyfork.org/users/49949-ipumpkin',
        version: '2025.07.15',
        author: 'iPumpkin',
        description: {
          en: 'Enhances reading experience on the "絵でわかる日本語" site by converting kanji readings from parentheses to ruby, hiding ads and clutter, and adding text-to-speech for selected text.',
          ja: '「絵でわかる日本語」サイト内の漢字の読みを括弧表記から自動でふりがなに変換し、広告や不要な要素を非表示にします。選択テキストの読み上げ機能にも対応し、快適な読書体験を実現します。',
          'zh-CN': '将「絵でわかる日本語」网站中的汉字注音由括号形式自动转换为振假名，隐藏广告和无关元素，并支持划词朗读功能，提升阅读体验。',
          'zh-TW': '將「絵でわかる日本語」網站中的漢字注音由括號形式自動轉換為振假名，隱藏廣告與無關元素，並支援劃詞朗讀功能，提升閱讀體驗。',
          ko: '「絵でわかる日本語」웹사이트에서 한자 읽기를 괄호 표기에서 루비로 변환하고, 광고 및 불필요한 요소를 숨기며, 선택한 텍스트에 대한 텍스트 음성 변환 기능을 추가하여 읽기 경험을 향상시킵니다.',
          vi: 'Cải thiện trải nghiệm đọc trên trang web「絵でわかる日本語」bằng cách chuyển đổi cách đọc kanji từ dấu ngoặc đơn thành ruby, ẩn quảng cáo và các yếu tố không cần thiết, đồng thời thêm tính năng text-to-speech cho văn bản được chọn.',
        },
        icon: 'https://livedoor.blogimg.jp/edewakaru/imgs/8/c/8cdb7924.png',
        match: 'https://www.edewakaru.com/*',
        license: 'GPL-3.0',
        'run-at': 'document-start',
      },
      build: {
        // 构建时不压缩代码，符合 GreasyFork 的要求
        minify: false,
        // 自动检测并添加使用的 GM API 到 @grant 声明
        autoGrant: true,
      },
      server: {
        // 将 GM API 挂载为全局变量，避免每个模块都需要导入
        mountGmApi: true,
      },
    }),
  ],
})
