/**
 * @module PageOptimizer
 * @description 负责页面布局优化、样式注入和无关元素清理
 * 1. 立即执行：在页面加载初期 document-start，立即注入 CSS 规则，隐藏所有已知的不需要元素，实现无闪烁的视觉体验
 * 2. 延迟清理：在文档对象模型完全加载后 DOMContentLoaded，从结构上彻底移除所有被隐藏的元素以及页面中的脚本
 */

const PageOptimizer = {
  _config: {
    MODULE_ENABLED: true,
    GLOBAL_REMOVE_SELECTORS: ['header#blog-header', 'footer#blog-footer', '.ldb_menu', '#analyzer_tags', '#gdpr-banner', '.adsbygoogle', '#ad_rs', '#ad2', 'div[class^="fluct-unit"]', '.article-social-btn', 'iframe[src*="clap.blogcms.jp"]', '#article-options', 'a[href*="blogmura.com"]', 'a[href*="with2.net"]', 'div[id^="ldblog_related_articles_"]'],
    STYLES: `
      #container { width: 100%; }
      @media (min-width: 960px) { #container { max-width: 960px; } }
      @media (min-width: 1040px) { #container { max-width: 1040px; } }
      #content { display: flex; position: relative; padding: 50px 0 !important; }
      #main { flex: 1; float: none !important; width: 100% !important; }
      aside#sidebar { visibility: hidden; float: none !important; width: 350px !important; flex: 0 0 350px; }
      .plugin-categorize { position: fixed; height: 85vh; display: flex; flex-direction: column; padding: 0 !important; width: 350px !important; }
      .plugin-categorize .side { flex: 1; overflow-y: auto; max-height: unset; }
      .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) { margin-top: 5px; margin-bottom: 0; }
      .article { padding: 0 0 20px 0 !important; margin-bottom: 30px !important; }
      .article-body { padding: 0 !important; }
      .article-pager { margin-bottom: 0 !important; }
      .article-body-inner { line-height: 2; opacity: 0; transition: opacity 0.3s; }
      .article-body-inner img.pict { margin: 0 !important; width: 80% !important; display: block; }
      .article-body-inner strike { color: orange !important; }
      .article-body-inner em { font-style: normal !important; font-weight: bold !important; color: red; }
      .to-pagetop { position: fixed; bottom: 19.2px; right: 220px; z-index: 9999; }
      rt, iframe, time, .pager, #sidebar { -webkit-user-select: none; user-select: none; }
      .article-body-inner:after, .article-meta:after, #container:after, #content:after, article:after, section:after, .cf:after { content: none !important; display: none !important; height: auto !important; visibility: visible !important; }
    `,
  },

  /**
   * 初始化模块，注入CSS规则
   */
  init() {
    if (!this._config.MODULE_ENABLED) return

    // 注入防闪烁CSS
    const antiFlickerCss = `${this._config.GLOBAL_REMOVE_SELECTORS.join(', ')} { display: none !important; }`
    GM_addStyle(antiFlickerCss)

    // 注入主样式
    GM_addStyle(this._config.STYLES)
  },

  /**
   * 负责执行一次性的全局 DOM 清理任务
   */
  cleanupGlobalElements() {
    if (!this._config.MODULE_ENABLED) return

    // 1. 根据配置列表，移除所有匹配的垃圾组件
    document.querySelectorAll(this._config.GLOBAL_REMOVE_SELECTORS.join(',')).forEach((el) => el.remove())

    // 2. 移除 <body> 内所有不再需要的、可能引起副作用的标签
    document.querySelectorAll('body script, body link, body style, body noscript').forEach((el) => el.remove())
  },

  /**
   * 负责单篇文章容器的收尾工作
   * @param {HTMLElement} container - 文章 `.article-body-inner` 容器元素
   */
  cleanupArticleBody(container) {
    if (!this._config.MODULE_ENABLED) return

    // 清理外层容器本身的头尾
    this._trimContainerBreaks(container)

    // 找到最后一个子元素，清理它的头尾
    const lastElement = container.lastElementChild
    if (lastElement) {
      this._trimContainerBreaks(lastElement)
    }

    // 显示容器
    container.style.opacity = 1
  },

  /**
   * 清理容器开头和结尾多余的换行和空白节点
   * @param {HTMLElement} element - 任何需要被清理头尾的 DOM 元素
   * @private
   */
  _trimContainerBreaks(element) {
    // 安全检查，如果传入的不是一个有效元素，则直接返回
    if (!element) return

    // 判断节点是否为"垃圾"
    const isJunkNode = (node) => {
      if (!node) return true

      // 检查是否为"纯空白"的文本节点
      if (node.nodeType === 3 && /^\s*$/.test(node.textContent)) {
        return true
      }

      // 检查是否为元素节点，并且是我们定义的"垃圾"标签
      if (node.nodeType === 1) {
        const tagName = node.tagName
        if (tagName === 'BR') return true
        if (tagName === 'SPAN' && /^\s*$/.test(node.textContent)) return true
        if (tagName === 'A' && /^\s*$/.test(node.textContent)) return true
      }

      return false
    }

    // 从开头移除所有垃圾节点
    while (element.firstChild && isJunkNode(element.firstChild)) {
      element.removeChild(element.firstChild)
    }

    // 从结尾移除所有垃圾节点
    while (element.lastChild && isJunkNode(element.lastChild)) {
      element.removeChild(element.lastChild)
    }
  },

  /**
   * 优化侧边栏，只保留分类并使其可见
   */
  finalizeLayout() {
    if (!this._config.MODULE_ENABLED) return

    const sidebar = document.querySelector('aside#sidebar')
    if (!sidebar) return

    const category = sidebar.querySelector('.plugin-categorize')

    // 清空侧边栏现有内容
    sidebar.innerHTML = ''

    if (category) {
      // 只将分类插件加回去
      sidebar.appendChild(category)

      // 使侧边栏可见
      sidebar.style.visibility = 'visible'
    }
  },
}

export default PageOptimizer
