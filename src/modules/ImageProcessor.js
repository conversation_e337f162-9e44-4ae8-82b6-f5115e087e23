/**
 * @module ImageProcessor
 * @description 专门处理博客图片链接，将其转换为直接的图片元素
 * 此模块查找页面中指向 livedoor 图床的链接，并将它们替换为高质量的 `<img>` 标签，
 * 从而优化图片加载和显示体验
 */

const ImageProcessor = {
  _config: {
    MODULE_ENABLED: true,
    // 匹配 livedoor 缩略图链接的正则表达式
    IMG_SRC_REGEX: /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i,
  },

  /**
   * 处理指定容器内的所有图片链接
   * @param {HTMLElement} container - 包含图片链接的容器元素
   */
  process(container) {
    if (!this._config.MODULE_ENABLED) return

    container.querySelectorAll('a[href*="livedoor.blogimg.jp"]').forEach((link) => {
      const img = link.querySelector('img.pict')
      if (!img) return

      // 创建新的图片元素
      const newImg = document.createElement('img')
      newImg.loading = 'lazy'

      // 移除 '-s' 后缀以获取原图
      newImg.src = img.src.replace(this._config.IMG_SRC_REGEX, '$1$2')

      // 继承原有属性
      newImg.alt = (img.alt || '').replace(/blog/gi, '')
      Object.assign(newImg, {
        className: img.className,
        width: img.width,
        height: img.height,
      })

      // 用新的图片元素替换整个链接
      link.replaceWith(newImg)
    })
  },
}

export default ImageProcessor
