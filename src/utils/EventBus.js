/**
 * @module EventBus
 * @description 事件发布/订阅系统，用于模块间解耦通信
 * 允许模块通过事件机制而非直接依赖进行通信
 */

const EventBus = {
  events: {},

  /**
   * 订阅事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 事件回调函数
   * @returns {Function} 返回取消订阅的函数
   */
  subscribe(event, callback) {
    if (!this.events[event]) {
      this.events[event] = []
    }
    this.events[event].push(callback)

    // 返回取消订阅的函数，方便后续清理
    return () => this.unsubscribe(event, callback)
  },

  /**
   * 取消订阅事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 要取消的回调函数
   */
  unsubscribe(event, callback) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter((cb) => cb !== callback)
      // 如果没有订阅者了，清理事件对象
      if (this.events[event].length === 0) {
        delete this.events[event]
      }
    }
  },

  /**
   * 发布事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  publish(event, data) {
    if (this.events[event]) {
      // 创建副本避免在回调中修改订阅列表导致的问题
      const subscribers = [...this.events[event]]
      subscribers.forEach((callback) => {
        try {
          callback(data)
        } catch (error) {
          console.error(`[ERROR] [EventBus] 事件处理器执行错误 (${event}):`, error)
        }
      })
      return true
    }
    return false
  },

  /**
   * 清理所有事件订阅
   * 通常在需要重置系统状态时使用
   */
  clear() {
    this.events = {}
  },
}

export default EventBus
